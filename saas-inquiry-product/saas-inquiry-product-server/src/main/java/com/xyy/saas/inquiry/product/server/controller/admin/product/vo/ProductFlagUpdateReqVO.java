package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 商品属性标志修改 Request VO")
@Data
public class ProductFlagUpdateReqVO {

    @Schema(description = "商品编号列表", requiredMode = RequiredMode.REQUIRED, example = "21893")
    @NotEmpty(message = "商品编号列表不能为空")
    private List<Long> ids;


    // 禁采状态：0-未禁采 1-总部禁采 2-门店禁采 3-总部门店禁采
    @Schema(description = "禁采状态：0-未禁采 1-总部禁采 2-门店禁采 3-总部门店禁采", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer purchaseDisabled;

    @Schema(description = "是否停售", example = "true")
    private Boolean stopSale;

    @Schema(description = "是否特价商品", example = "true")
    private Boolean specialPrice;

    @Schema(description = "是否积分商品", example = "true")
    private Boolean integral;

    /**
     * 转换属性
     * @return
     */
    public ProductFlag mappingProductFlag() {
        ProductFlag pf = new ProductFlag()
            .setStopSale(this.getStopSale())
            .setSpecialPrice(this.getSpecialPrice())
            .setIntegral(this.getIntegral());

        return pf.mappingPurchaseDisabled(this.getPurchaseDisabled());
    }

}