-- 业务类型配置表
DROP TABLE IF EXISTS `system_business_type`;
CREATE TABLE `system_business_type` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
                                      `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
                                      `client_type` tinyint NOT NULL COMMENT '客户端类型',
                                      `business_type` tinyint NOT NULL COMMENT '业务类型',
                                      `user_type` tinyint NOT NULL COMMENT '用户类型',
                                      `client_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端编号',
                                      `client_secret` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
                                      `agent_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理编号',
                                      `status` tinyint NOT NULL COMMENT '状态',
                                      `wx_mobile_auth` bit(1) NOT NULL DEFAULT b'0' COMMENT '微信手机号授权开关（0-禁用，1-启用）',
                                      `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
                                      `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
                                      `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                      `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `uk_client_business_user_type` (`client_type`, `business_type`, `user_type`, `tenant_id`) COMMENT '客户端类型、业务类型、用户类型唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务类型配置表';

-- 插入示例数据
INSERT INTO `system_business_type` (`name`, `client_type`, `business_type`, `user_type`, `client_id`, `client_secret`,`agent_id`, `status`, `creator`, `tenant_id`) VALUES
                                                                                                                                                           ('荷叶问诊微信小程序', 2, 0, 1, 'wxd1425c818778593e', '32ab10e980c95f7eeb6d7163d35c2649',
                                                                                                                                                            null,
                                                                                                                                                            0, 'system', 0),
                                                                                                                                                           ('药帮忙微信小程序', 2, 4, 1, 'wx096711614039b8bb', '63fe44973073c01771b8838199b3f8ee',null,
                                                                                                                                                            0, 'system', 0);
-- 修改租户表结构，给账号数量默认值
alter table xyy_saas_system_test.system_tenant
  change column wz_account_count wz_account_count int(11) not null default 100 comment '账号数量',
  change column zhl_account_count zhl_account_count int(11) not null default 100 comment '智慧脸账号数量';

alter table xyy_saas_system_test.system_tenant_biz_relation
  change column account_count account_count int(11) not null default 100 comment '账号数量';
