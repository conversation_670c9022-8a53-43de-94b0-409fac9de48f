###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "18071153100",
  "password": "153100"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


###  2.0. 问诊接口限制
POST {{baseAdminKernelUrl}}/kernel/patient/inquiry/patient-check
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "prescriptionType": "1",
  "patientPref": "HZ119582",
  "patientName": "陈笑一",
  "patientMobile": "18636363973",
  "patientIdCard": "******************",
  "patientAge": 29,
  "patientSex": 1
}

> {%
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.查商品信息 - 西药
GET {{baseAppSystemUrl}}/product/search/products-by-name-spec?productName=阿莫西林胶囊&medicineType=0&pageNo=1&pageSize=100
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("无商品信息");
  }
  let product = response.body.data.list[0];

  client.global.set("pref", product.pref);
  client.global.set("productName", product.productName);
  client.global.set("commonName", product.commonName);

  let inquiryProductInfo = {
    "inquiryProductInfos": [{
      "pref": product.pref,
      "standardId": product.pref,
      "commonName": product.commonName,
      "productName": product.productName,
      "attributeSpecification": product.attributeSpecification,
      "manufacturer": product.manufacturer,
      "unitName": product.unitName,
      "quantity": 1,
      "directions": "口服",
      "singleDose": "10",
      "singleUnit": "袋",
      "useFrequency": "1次/6小时",
      "useFrequencyValue": "ONETIMESSIXHOURS",
    }],
    "tcmTotalDosage": "10",
    "tcmDailyDosage": "3",
    "tcmUsage": "2",
    "tcmDirections": "温服",
    "tcmProcessingMethod": "打粉冲服"
  }
  client.global.set("inquiryProductInfo", JSON.stringify(inquiryProductInfo));
%}

### 3.根据药品查询关联诊断
GET  {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis/common-diagnosis?diagnosisType=0&prescriptionType=1&tenantId=1943202113363873794
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}


### 3.根据药品查询关联诊断
POST  {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis/recommend-diagnosis
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "pageNo": 1,
  "pageSize": 10,
  "prescriptionType": 1,
  "productSearchList": [
    {
      "pref": "{{pref}}",
      "medicineType": 0,
      "productName": "{{commonName}}"
    }
  ]
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let diagnosis = [{
      "diagnosisCode": response.body.data[0].diagnosisCode,
      "diagnosisName": response.body.data[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}



### 3.查系统诊断 - 模拟手选
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/pages?diagnosisType=1&prescriptionType=1
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0 && !client.global.get("diagnosis")) {
    let diagnosis = [{
      "diagnosisCode": response.body.data.list[0].diagnosisCode,
      "diagnosisName": response.body.data.list[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}


### 4.查主诉
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-main-suit/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0) {
    let mainSuit = [response.body.data.list[0].mainSuitName]
    client.global.set("mainSuit", JSON.stringify(mainSuit));
  }
%}


### 5.查过敏史 - 使用推荐过敏史
GET {{baseAppKernelUrl}}/kernel/hospital/rational/irritability/getRecommendAllergy
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let allergic = [response.body.data[0].value]
    client.global.set("allergic", JSON.stringify(allergic));
  }
%}


### 6.0.去问诊下单 前置校验
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry-pre-check
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 0,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "游客1",
      "patientMobile": "13435637493",
      "patientIdCard": "******************",
      "patientAge": "19",
      "patientSex": 1
    },
    "mainSuit": [],
    "allergic": [],
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 0,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 0,
    "prescriptionType": 1,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data.inquiryPref);
%}



### 6.去问诊下单
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 0,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "游客1",
      "patientMobile": "13435637493",
      "patientIdCard": "******************",
      "patientAge": "29",
      "patientSex": 1
    },
    "mainSuit": [],
    "allergic": [],
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 0,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 0,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data.inquiryPref);
%}


###  1.医生登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926351002",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.医生出诊
POST {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/start-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "userId": "{{loginUserId}}",
  "autoGrabStatus": "0",
  "inquiryWayTypeItems": [1,2]
}

> {%
  console.log(response.body.code)
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 3.医生抢单 - 问诊单号取 门店去问诊返回得单号
PUT {{baseAppKernelUrl}}/kernel/hospital/prescription/grabbing-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": "113392",
  "clientChannelType": "0",
  "clientOsType": "android"
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }

%}

### 4.医生开具门诊病例
POST {{baseAppKernelUrl}}/kernel/hospital/inquiry-clinical-case/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": 112139,
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": {{mainSuit}},
  "diagnosis": {{diagnosis}},
  "allergic": {{allergic}},
  "ext": {
    "outpatientDiagnosisDesc": "门诊诊断说明"
  },
  "measures": "处理措施",
  "observation": "0",
  "referral": "0",
  "tcmSyndromeCode": "B02.03.02.01",
  "tcmSyndromeName": "寒湿外侵证",
  "tcmTreatmentMethodCode": "C03.02.10",
  "tcmTreatmentMethodName": "点穴疗法"
}



### 4.医生开方
POST {{baseAppKernelUrl}}/kernel/hospital/prescription/issues-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": 113392,
  "medicineType": 0,
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": [
    "111"
  ],
  "diagnosis": [
    {
      "diagnosisCode": "1",
      "xx": "2"
    }
  ],
  "clientChannelType": 0
}


### 5.医生停诊
PUT {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/stop-receipt?userId={{loginUserId}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

