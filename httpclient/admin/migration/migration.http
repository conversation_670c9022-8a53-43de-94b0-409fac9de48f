### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "develop",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### test-organ
GET {{baseAdminKernelUrl}}/kernel/pharmacist/inquiry-migration/test-organ?organSign=LZA00116145
Authorization: Bearer {{token}}


### migration
POST {{baseAdminKernelUrl}}/kernel/pharmacist/inquiry-migration/migration
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "migrationType": 1,
  "migrationAction": 2,
  "startTime": "2025-06-30 00:00:00"
}

